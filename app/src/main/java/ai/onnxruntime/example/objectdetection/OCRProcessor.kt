package ai.onnxruntime.example.objectdetection

import ai.onnxruntime.OnnxTensor
import ai.onnxruntime.OrtEnvironment
import ai.onnxruntime.OrtSession
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.nio.FloatBuffer

/**
 * OCRProcessor - Tương đương với code Python main()
 * Tích hợp tất cả các chức năng: load vocab, preprocess image, run inference, decode
 */
class OCRProcessor(private val context: Context) {

    private var ortSession: OrtSession? = null
    private var vocab: Vocab? = null
    private var isInitialized = false

    companion object {
        private const val TAG = "OCRProcessor"
        private const val MODEL_NAME = "full_ocr.onnx"
        private const val VOCAB_NAME = "vocab.json"
    }

    /**
     * Khởi tạo model và vocab - tương đương loadVocab() và tạo session trong Python
     */
    fun initialize(): Boolean {
        return try {
            Log.d(TAG, "Đang khởi tạo OCR Processor...")

            // Load vocab
            vocab = loadVocab()
            if (vocab == null) {
                Log.e(TAG, "Không thể load vocab")
                return false
            }

            // Load model
            ortSession = loadModel()
            if (ortSession == null) {
                Log.e(TAG, "Không thể load model")
                return false
            }

            isInitialized = true
            Log.d(TAG, "OCR Processor đã sẵn sàng!")
            true

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi khởi tạo OCR Processor: ${e.message}", e)
            false
        }
    }

    /**
     * Load vocab từ file JSON - tương đương load_vocab() trong Python
     */
    private fun loadVocab(): Vocab? {
        return try {
            Log.d(TAG, "Đang load vocab...")

            val vocabFile = File(context.cacheDir, VOCAB_NAME)
            if (!vocabFile.exists()) {
                // Copy từ raw resource
                context.resources.openRawResource(R.raw.vocab).use { input ->
                    FileOutputStream(vocabFile).use { output ->
                        input.copyTo(output)
                    }
                }
            }

            val jsonStr = vocabFile.readText()
            val jsonObj = JSONObject(jsonStr)

            val idx2char = mutableMapOf<Int, String>()
            val keys = jsonObj.keys()
            while (keys.hasNext()) {
                val k = keys.next()
                idx2char[k.toInt()] = jsonObj.getString(k)
            }

            Vocab(idx2char)

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi load vocab: ${e.message}", e)
            null
        }
    }

    /**
     * Load ONNX model - tương đương tạo ort.InferenceSession trong Python
     */
    private fun loadModel(): OrtSession? {
        return try {
            Log.d(TAG, "Đang load model...")

            val env = OrtEnvironment.getEnvironment()
            val modelFile = File(context.cacheDir, MODEL_NAME)

            if (!modelFile.exists()) {
                // Copy từ raw resource
                context.resources.openRawResource(R.raw.full_ocr).use { input ->
                    FileOutputStream(modelFile).use { output ->
                        input.copyTo(output)
                    }
                }
            }

            env.createSession(modelFile.absolutePath, OrtSession.SessionOptions())

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi load model: ${e.message}", e)
            null
        }
    }

    /**
     * Preprocess image - tương đương preprocess_image() trong Python
     */
    private fun preprocessImage(bitmap: Bitmap): OnnxTensor? {
        return try {
            val env = OrtEnvironment.getEnvironment()
            val w = bitmap.width
            val h = bitmap.height
            // buffer cho [1, H, W, 3]
            val buf = FloatBuffer.allocate(1 * h * w * 3)

            val pixels = IntArray(w * h)
            bitmap.getPixels(pixels, 0, w, 0, 0, w, h)

            // thứ tự y → x → channel (NHWC)
            for (y in 0 until h) {
                for (x in 0 until w) {
                    val idx = y * w + x
                    val p = pixels[idx]

                    val r = (p shr 16 and 0xFF).toFloat()
                    val g = (p shr 8 and 0xFF).toFloat()
                    val b = (p and 0xFF).toFloat()


                    buf.put(r)
                    buf.put(g)
                    buf.put(b)
                }
            }
            buf.rewind()

            // shape: [1, H, W, 3]
            val shape = longArrayOf(1, h.toLong(), w.toLong(), 3)
            return OnnxTensor.createTensor(env, buf, shape)

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi preprocess image: ${e.message}", e)
            null
        }
    }

    /**
     * Decode token IDs thành text - tương đương decode() trong Python
     */
    private fun decode(tokenIds: IntArray): String {
        return vocab?.decode(tokenIds.toList()) ?: ""
    }

    /**
     * Chạy OCR cho một ảnh - tương đương main() trong Python
     */
    fun runOCR(bitmap: Bitmap): String {
        if (!isInitialized || ortSession == null || vocab == null) {
            Log.e(TAG, "OCR Processor chưa được khởi tạo")
            return "Lỗi: OCR Processor chưa sẵn sàng"
        }

        return try {
            Log.d(TAG, "Bắt đầu preprocessing image...")
            val inputTensor = preprocessImage(bitmap)
            if (inputTensor == null) {
                return "Lỗi: Không thể preprocess image"
            }

            Log.d(TAG, "Chạy inference...")
            val inputName = ortSession!!.inputNames.first()
            val outputs = ortSession!!.run(mapOf(inputName to inputTensor))

            // Lấy output tokens
            val outputValue = outputs[0].value
            val longArrays = outputValue as Array<LongArray>
            Log.d(TAG, "Output value: $outputValue")
            Log.d(TAG, "Long arrays: $longArrays")
            val tokenIds = longArrays[0].map { it.toInt() }.toIntArray()

            Log.d(TAG, "Token IDs: ${tokenIds.joinToString()}")

            // Decode thành text
            val text = decode(tokenIds)
            Log.d(TAG, "OCR Result: $text")

            text

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi chạy OCR: ${e.message}", e)
            "Lỗi: ${e.message}"
        }
    }

    /**
     * Chạy OCR cho ảnh từ assets - tiện ích bổ sung
     */
    fun runOCRFromAssets(imagePath: String): String {
        return try {
            val inputStream = context.assets.open(imagePath)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()

            if (bitmap == null) {
                "Lỗi: Không thể load ảnh $imagePath"
            } else {
                runOCR(bitmap)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi load ảnh từ assets: ${e.message}", e)
            "Lỗi: ${e.message}"
        }
    }

    /**
     * Giải phóng tài nguyên
     */
    fun cleanup() {
        try {
            ortSession?.close()
            ortSession = null
            vocab = null
            isInitialized = false
            Log.d(TAG, "OCR Processor đã được cleanup")
        } catch (e: Exception) {
            Log.e(TAG, "Lỗi cleanup: ${e.message}", e)
        }
    }
}
