package ai.onnxruntime.example.objectdetection

class Vocab(private val idx2char: Map<Int, String>) {
    fun decode(ids: List<Int>): String {
        val ignoreTokens = setOf("<PAD>", "<SOS>", "<EOS>")
        val chars = mutableListOf<String>()

        for (id in ids) {
            // Áp dụng logic giống Python: id - 4
            val adjustedId = id - 4
            val ch = idx2char[adjustedId] ?: ""
            if (ch.isNotEmpty() && ch !in ignoreTokens) {
                chars.add(ch)
            }
        }

        return chars.joinToString("")
    }
}
