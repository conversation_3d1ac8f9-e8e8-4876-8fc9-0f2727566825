package ai.onnxruntime.example.objectdetection

import android.graphics.BitmapFactory
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.util.Log
import android.widget.Toast


class MainActivity : AppCompatActivity() {
    private var ocrProcessor: OCRProcessor? = null

    private lateinit var imageView: ImageView
    private lateinit var textView: TextView

    private var imageList = listOf("img_0.png", "img_1.png", "img_2.png", "img_0.jpg", "img_1.jpg", "img_2.jpg", "img_3.jpg", "img_4.jpg", "img_5.jpg", "img_6.jpg", "img_7.jpg")
    private var currentIndex = 0
    private var isModelLoaded = false

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        imageView = findViewById(R.id.imageView)
        textView = findViewById(R.id.resultText)
        val btnNext = findViewById<Button>(R.id.btnNext)
        val btnPrev = findViewById<Button>(R.id.btnPrev)

        initializeModel()

        btnNext.setOnClickListener {
            if (isModelLoaded) {
                currentIndex = (currentIndex + 1) % imageList.size
                showImageAndRunOCR()
            } else {
                showError("Model chưa được load. Vui lòng khởi động lại app.")
            }
        }

        btnPrev.setOnClickListener {
            if (isModelLoaded) {
                currentIndex = if (currentIndex - 1 < 0) imageList.size - 1 else currentIndex - 1
                showImageAndRunOCR()
            } else {
                showError("Model chưa được load. Vui lòng khởi động lại app.")
            }
        }
    }

    private fun initializeModel() {
        try {
            Log.d(TAG, "Bắt đầu khởi tạo OCR Processor...")
            textView.text = "Đang load model..."

            ocrProcessor = OCRProcessor(this)
            isModelLoaded = ocrProcessor!!.initialize()

            if (isModelLoaded) {
                textView.text = "Model đã sẵn sàng!"
                showImageAndRunOCR()
            } else {
                showError("Không thể khởi tạo OCR Processor")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi khởi tạo model: ${e.message}", e)
            isModelLoaded = false
            showError("Lỗi khởi tạo model: ${e.message}")
        }
    }

    private fun showImageAndRunOCR() {
        if (!isModelLoaded || ocrProcessor == null) {
            showError("OCR Processor chưa sẵn sàng")
            return
        }

        try {
            val imgName = imageList[currentIndex]
            Log.d(TAG, "Đang xử lý ảnh: $imgName")

            val inputStream = assets.open(imgName)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()

            if (bitmap == null) {
                showError("Không thể load ảnh: $imgName")
                return
            }

            imageView.setImageBitmap(bitmap)
            textView.text = "Đang xử lý ảnh $imgName..."

            // Sử dụng OCRProcessor để chạy OCR (tương đương main() trong Python)
            val text = ocrProcessor!!.runOCR(bitmap)

            textView.text = "Ảnh: $imgName\nKết quả OCR: $text"
            Log.d(TAG, "OCR hoàn thành: $text")

        } catch (e: Exception) {
            Log.e(TAG, "Lỗi khi xử lý ảnh: ${e.message}", e)
            showError("Lỗi xử lý ảnh: ${e.message}")
        }
    }

    private fun showError(message: String) {
        Log.e(TAG, message)
        textView.text = "❌ $message"
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    override fun onDestroy() {
        super.onDestroy()
        ocrProcessor?.cleanup()
    }
}
